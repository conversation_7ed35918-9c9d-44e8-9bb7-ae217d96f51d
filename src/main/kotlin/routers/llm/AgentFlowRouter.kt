package com.dzhp.permit.routers.llm

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sse.*
import io.ktor.sse.*
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Semaphore
import kotlinx.serialization.*
import kotlinx.serialization.json.*
import org.slf4j.LoggerFactory
import java.util.UUID
import com.dzhp.permit.services.ElasticSearchService

/**
 * 代理流程请求数据结构
 */
@Serializable
data class AgentFlowRequest(
    val agent_list: List<String>,
    val storage: StorageInfo,
    val permit_mee_data: PermitMeeData
)

@Serializable
data class StorageInfo(
    val username: String,
    val password: String,
    val data_id: String
)

@Serializable
data class PermitMeeData(
    val category: String,
    val data_id: String,
    val company_name: String,
    val review_status: String,
    val submit_time: String
)

/**
 * 单个代理调用请求数据结构
 */
@Serializable
data class AgentWebhookRequest(
    val user_id: String,
    val request_id: String,
    val agent_id: String,
    val username: String,
    val password: String,
    val data_id: String
)

/**
 * SSE事件数据结构
 */
@Serializable
data class SSEStartEvent(
    val type: String = "start",
    val request_id: String,
    val total_agents: Int,
    val message: String
)

@Serializable
data class SSEAgentErrorEvent(
    val type: String = "agent_error",
    val agent_id: String,
    val agent_index: Int,
    val request_id: String,
    val error: String
)

@Serializable
data class SSECompleteEvent(
    val type: String = "complete",
    val request_id: String,
    val message: String
)

@Serializable
data class SSEErrorEvent(
    val type: String = "error",
    val request_id: String,
    val error: String
)

@Serializable
data class SSEProgressEvent(
    val type: String = "progress_bar",
    val request_id: String,
    val agent_id: String,
    val progress: Int, // 0-100
    val message: String
)

/**
 * 完整响应日志数据结构
 * 用于记录到Elasticsearch的permit_mee_agent_full_response索引
 */
@Serializable
data class AgentFullResponseLog(
    val user_id: String,
    val request_id: String,
    val timestamp: Long,
    val username: String,
    val data_id: String,
    val full_response: String,
    val permit_mee_data: PermitMeeData
)

/**
 * 代理流程路由
 * 提供流式文本接口，将请求转发到外部服务
 *
 * - POST /api/run/flow/permit/mee : 流式文本接口，需要user-jwt认证
 * - POST /sse/n/run/flow : SSE流式接口，并发调用多个代理，需要user-llm-jwt认证
 */
fun Application.agentFlowRouter() {
    val logger = LoggerFactory.getLogger("AgentFlowRouter")
    val baseUrl = environment.config.property("api.n8n.url").getString()
    val elasticSearchService = ElasticSearchService(this)

    // 创建HTTP客户端
    val httpClient = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                prettyPrint = false
            })
        }
        // 设置超时时间
        engine {
            requestTimeout = 0 // 无超时时间，允许长连接
        }
    }

    routing {
        authenticate("user-llm-jwt") {
            /**
             * SSE接口，使用标准SSE响应
             * 并发调用多个代理，每当有代理完成返回时，直接流式返回其内容
             */
            sse("/sse/n/run/flow") {
                // 生成UUID4作为请求ID
                val requestId = UUID.randomUUID().toString()

                // 从 JWT 中提取用户ID
                val userId = call.principal<JWTPrincipal>()?.getClaim("code", String::class)?.toString() ?: "unknown"

                try {
                    // 从查询参数获取数据（SSE通常使用GET请求）
                    val agentListParam = call.request.queryParameters["agent_list"] ?: "[]"
                    val usernameParam = call.request.queryParameters["username"] ?: ""
                    val passwordParam = call.request.queryParameters["password"] ?: ""
                    val dataIdParam = call.request.queryParameters["data_id"] ?: ""

                    // 解析agent_list
                    val agentList = try {
                        Json.parseToJsonElement(agentListParam).jsonArray.map { it.jsonPrimitive.content }
                    } catch (e: Exception) {
                        logger.error("解析agent_list失败: $agentListParam", e)
                        emptyList()
                    }

                    if (agentList.isEmpty()) {
                        send(ServerSentEvent(
                            data = Json.encodeToString(SSEErrorEvent(
                                request_id = requestId,
                                error = "agent_list不能为空"
                            )),
                            event = "error"
                        ))
                        return@sse
                    }

                    // 构建请求对象
                    val request = AgentFlowRequest(
                        agent_list = agentList,
                        storage = StorageInfo(
                            username = usernameParam,
                            password = passwordParam,
                            data_id = dataIdParam
                        ),
                        permit_mee_data = PermitMeeData(
                            category = call.request.queryParameters["category"] ?: "",
                            data_id = dataIdParam,
                            company_name = call.request.queryParameters["company_name"] ?: "",
                            review_status = call.request.queryParameters["review_status"] ?: "",
                            submit_time = call.request.queryParameters["submit_time"] ?: ""
                        )
                    )

                    logger.info("收到SSE代理流程请求: request_id=$requestId, user_id=$userId, agents=${request.agent_list}")

                    // 发送开始事件
                    send(ServerSentEvent(
                        data = Json.encodeToString(SSEStartEvent(
                            request_id = requestId,
                            total_agents = request.agent_list.size,
                            message = "预审核流程创建完毕, 共 ${request.agent_list.size} 个审核项, 开始预审核 ......"
                        )),
                        event = "start"
                    ))

                    // 并发调用所有代理，限制并发数为5
                    val concurrencyLimit = 5
                    val semaphore = Semaphore(concurrencyLimit)

                    val jobs = request.agent_list.mapIndexed { index, agentId ->
                        async {
                            semaphore.acquire()
                            try {
                                callAgentWebhook(
                                    httpClient = httpClient,
                                    baseUrl = baseUrl,
                                    agentId = agentId,
                                    userId = userId,
                                    requestId = requestId,
                                    request = request,
                                    logger = logger,
                                    agentIndex = index,
                                    progressCallback = { progressEvent ->
                                        send(ServerSentEvent(
                                            data = Json.encodeToString(progressEvent),
                                            event = "progress_bar"
                                        ))
                                    }
                                )
                            } finally {
                                semaphore.release()
                            }
                        }
                    }

                    // 收集所有代理响应用于记录到Elasticsearch
                    val allResponses = mutableListOf<String>()

                    // 等待每个代理完成并流式返回结果
                    jobs.forEachIndexed { index, job ->
                        try {
                            val result = job.await()

                            // 收集响应数据
                            allResponses.add(result)

                            // 发送代理完成事件
                            send(ServerSentEvent(
                                data = result,
                                event = "agent_complete"
                            ))

                            logger.info("代理完成: request_id=$requestId, agent_id=${request.agent_list[index]}")

                        } catch (e: Exception) {
                            logger.error("代理调用失败: request_id=$requestId, agent_id=${request.agent_list[index]}, error=${e.message}", e)

                            // 收集错误响应
                            allResponses.add("Error: ${e.message ?: "Unknown error"}")

                            // 发送代理错误事件
                            send(ServerSentEvent(
                                data = Json.encodeToString(SSEAgentErrorEvent(
                                    agent_id = request.agent_list[index],
                                    agent_index = index,
                                    request_id = requestId,
                                    error = e.message ?: "Unknown error"
                                )),
                                event = "agent_error"
                            ))
                        }
                    }

                    // 记录完整响应数据到Elasticsearch
                    val fullResponse = allResponses.joinToString("\n\n")
                    logFullResponseToElasticsearch(
                        elasticSearchService = elasticSearchService,
                        userId = userId,
                        requestId = requestId,
                        username = request.storage.username,
                        dataId = request.storage.data_id,
                        fullResponse = fullResponse,
                        permitMeeData = request.permit_mee_data,
                        logger = logger
                    )

                    // 发送完成事件
                    send(ServerSentEvent(
                        data = Json.encodeToString(SSECompleteEvent(
                            request_id = requestId,
                            message = "预审核已完成，结果已保存。请注意，此预审核由AI辅助完成，结果仅供参考，最终判断请以人工审核为准。"
                        )),
                        event = "complete"
                    ))

                    logger.info("SSE代理流程请求处理完成: request_id=$requestId, user_id=$userId")

                } catch (e: Exception) {
                    logger.error("SSE代理流程请求处理失败: request_id=$requestId, user_id=$userId, error=${e.message}", e)

                    // 发送错误事件
                    send(ServerSentEvent(
                        data = Json.encodeToString(SSEErrorEvent(
                            request_id = requestId,
                            error = e.message ?: "Unknown error"
                        )),
                        event = "error"
                    ))
                }
            }
        }
    }
}

/**
 * 调用单个代理的webhook接口
 *
 * @param httpClient HTTP客户端
 * @param baseUrl 基础URL
 * @param agentId 代理ID
 * @param userId 用户ID
 * @param requestId 请求ID
 * @param request 原始请求数据
 * @param logger 日志记录器
 * @param agentIndex 代理索引
 * @param progressCallback 进度回调函数
 * @return 代理返回的内容
 */
private suspend fun callAgentWebhook(
    httpClient: HttpClient,
    baseUrl: String,
    agentId: String,
    userId: String,
    requestId: String,
    request: AgentFlowRequest,
    logger: org.slf4j.Logger,
    agentIndex: Int,
    progressCallback: suspend (SSEProgressEvent) -> Unit
): String {
    val webhookUrl = "$baseUrl/webhook/agent"

    // 构建代理调用请求
    val agentRequest = AgentWebhookRequest(
        user_id = userId,
        request_id = "${requestId}_${agentId}",
        agent_id = agentId,
        username = request.storage.username,
        password = request.storage.password,
        data_id = request.storage.data_id
    )

    logger.info("调用代理webhook: agent_id=$agentId, url=$webhookUrl, request_id=$requestId")

    try {
        // 发送开始进度事件
        progressCallback(SSEProgressEvent(
            request_id = requestId,
            agent_id = agentId,
            progress = 0,
            message = "开始调用代理 $agentId ..."
        ))

        // 模拟8秒前的假进度
        val progressJob = async {
            val progressSteps = listOf(
                Pair(10, "正在连接代理服务..."),
                Pair(20, "正在验证请求参数..."),
                Pair(30, "正在准备数据..."),
                Pair(40, "正在发送请求..."),
                Pair(50, "等待代理响应...")
            )

            for ((progress, message) in progressSteps) {
                delay(1500) // 每1.5秒更新一次进度
                progressCallback(SSEProgressEvent(
                    request_id = requestId,
                    agent_id = agentId,
                    progress = progress,
                    message = message
                ))
            }
        }

        // 实际调用代理
        val response = httpClient.post(webhookUrl) {
            contentType(ContentType.Application.Json)
            setBody(agentRequest)
            // 添加请求头
            header("X-Request-ID", requestId)
            header("X-User-ID", userId)
            header("X-Agent-ID", agentId)
        }

        // 取消进度模拟
        progressJob.cancel()

        // 发送LLM处理进度
        progressCallback(SSEProgressEvent(
            request_id = requestId,
            agent_id = agentId,
            progress = 80,
            message = "代理正在处理数据..."
        ))

        val responseText = response.bodyAsText()

        // 发送完成进度事件
        progressCallback(SSEProgressEvent(
            request_id = requestId,
            agent_id = agentId,
            progress = 100,
            message = "代理 $agentId 处理完成"
        ))

        logger.info("代理webhook调用成功: agent_id=$agentId, request_id=$requestId, status=${response.status}")

        return responseText

    } catch (e: Exception) {
        // 发送错误进度事件
        progressCallback(SSEProgressEvent(
            request_id = requestId,
            agent_id = agentId,
            progress = -1, // 使用-1表示错误
            message = "代理 $agentId 调用失败: ${e.message}"
        ))

        logger.error("代理webhook调用失败: agent_id=$agentId, request_id=$requestId, error=${e.message}", e)
        throw e
    }
}

/**
 * 记录完整响应数据到Elasticsearch
 *
 * @param elasticSearchService ElasticSearch服务
 * @param userId 用户ID
 * @param requestId 请求ID
 * @param username 用户名
 * @param dataId 数据ID
 * @param fullResponse 完整响应数据
 * @param permitMeeData 许可证数据
 * @param logger 日志记录器
 */
private suspend fun logFullResponseToElasticsearch(
    elasticSearchService: ElasticSearchService,
    userId: String,
    requestId: String,
    username: String,
    dataId: String,
    fullResponse: String,
    permitMeeData: PermitMeeData,
    logger: org.slf4j.Logger
) {
    try {
        val logData = AgentFullResponseLog(
            user_id = userId,
            request_id = requestId,
            timestamp = System.currentTimeMillis(),
            username = username,
            data_id = dataId,
            full_response = fullResponse,
            permit_mee_data = permitMeeData
        )

        val indexName = "permit_mee_agent_full_response"

        // 索引文档
        val response = elasticSearchService.indexDocument(indexName, logData, requestId)
        logger.info("完整响应数据已记录到Elasticsearch: index=$indexName, id=${response.id}, request_id=$requestId")

    } catch (e: Exception) {
        logger.error("记录完整响应数据到Elasticsearch失败: request_id=$requestId, error=${e.message}", e)
    }
}
